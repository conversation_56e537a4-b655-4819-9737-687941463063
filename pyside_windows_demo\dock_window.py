#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة مع أرصفة في PySide6
Dock Window in PySide6
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTextEdit,
    QLabel, QPushButton, QDockWidget, QListWidget, QTreeWidget,
    QTreeWidgetItem, QTableWidget, QTableWidgetItem, QSlider,
    QSpinBox, QCheckBox, QComboBox, QGroupBox, QSplitter
)
from PySide6.QtCore import Qt, QTimer, QDateTime
from PySide6.QtGui import QFont, QColor


class DockWindowDemo(QMainWindow):
    """نافذة رئيسية مع أرصفة قابلة للإرساء"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نافذة مع أرصفة - Dock Widgets")
        self.setGeometry(100, 100, 1000, 700)
        
        self.setup_central_widget()
        self.create_dock_widgets()
        self.create_menu_bar()
        self.create_status_bar()
        
        # تحديث الوقت
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
        
    def setup_central_widget(self):
        """إعداد الويدجت المركزي"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # العنوان
        title = QLabel("النافذة الرئيسية مع الأرصفة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)
        
        # الوصف
        description = QLabel("""
        الأرصفة (Dock Widgets) هي نوافذ فرعية يمكن إرساؤها على جوانب النافذة الرئيسية:
        
        ✓ قابلة للسحب والإفلات
        ✓ يمكن إرساؤها على أي جانب (يسار، يمين، أعلى، أسفل)
        ✓ يمكن جعلها عائمة (منفصلة)
        ✓ قابلة للإخفاء والإظهار
        ✓ يمكن تجميعها في تبويبات
        ✓ مفيدة للأدوات والنوافذ المساعدة
        
        جرب سحب الأرصفة وتحريكها لأماكن مختلفة!
        """)
        description.setWordWrap(True)
        description.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                padding: 20px;
                border-radius: 8px;
                border: 1px solid #bdc3c7;
                line-height: 1.6;
                color: #34495e;
                margin: 10px;
            }
        """)
        layout.addWidget(description)
        
        # منطقة العمل الرئيسية
        self.main_text_area = QTextEdit()
        self.main_text_area.setPlainText("""
مرحباً بك في منطقة العمل الرئيسية!

هذه هي المنطقة المركزية للنافذة. حولها توجد عدة أرصفة:

• رصيف الملفات (يسار): يعرض قائمة الملفات
• رصيف الخصائص (يمين): يعرض خصائص العنصر المختار  
• رصيف الأدوات (أسفل): يحتوي على أدوات مختلفة
• رصيف المعلومات (أعلى): يعرض معلومات النظام

يمكنك:
- سحب أي رصيف لتغيير موقعه
- النقر المزدوج على شريط عنوان الرصيف لجعله عائماً
- إغلاق الأرصفة من قائمة العرض
- تجميع الأرصفة في تبويبات

جرب التفاعل مع الأرصفة المختلفة!
        """)
        self.main_text_area.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #3498db;
                border-radius: 8px;
                padding: 15px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
                line-height: 1.5;
                margin: 10px;
            }
        """)
        layout.addWidget(self.main_text_area)
        
    def create_dock_widgets(self):
        """إنشاء الأرصفة المختلفة"""
        # رصيف الملفات (يسار)
        self.create_files_dock()
        
        # رصيف الخصائص (يمين)
        self.create_properties_dock()
        
        # رصيف الأدوات (أسفل)
        self.create_tools_dock()
        
        # رصيف المعلومات (أعلى)
        self.create_info_dock()
        
    def create_files_dock(self):
        """إنشاء رصيف الملفات"""
        files_dock = QDockWidget("الملفات", self)
        files_dock.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        
        files_widget = QWidget()
        layout = QVBoxLayout(files_widget)
        
        # شجرة الملفات
        files_tree = QTreeWidget()
        files_tree.setHeaderLabel("الملفات والمجلدات")
        
        # إضافة عناصر تجريبية
        root_item = QTreeWidgetItem(files_tree, ["المشروع"])
        
        src_item = QTreeWidgetItem(root_item, ["src"])
        QTreeWidgetItem(src_item, ["main.py"])
        QTreeWidgetItem(src_item, ["utils.py"])
        QTreeWidgetItem(src_item, ["config.py"])
        
        docs_item = QTreeWidgetItem(root_item, ["docs"])
        QTreeWidgetItem(docs_item, ["readme.md"])
        QTreeWidgetItem(docs_item, ["manual.pdf"])
        
        tests_item = QTreeWidgetItem(root_item, ["tests"])
        QTreeWidgetItem(tests_item, ["test_main.py"])
        QTreeWidgetItem(tests_item, ["test_utils.py"])
        
        files_tree.expandAll()
        files_tree.itemClicked.connect(self.on_file_selected)
        
        layout.addWidget(files_tree)
        
        files_dock.setWidget(files_widget)
        self.addDockWidget(Qt.LeftDockWidgetArea, files_dock)
        
        # حفظ مرجع للرصيف
        self.files_dock = files_dock
        
    def create_properties_dock(self):
        """إنشاء رصيف الخصائص"""
        properties_dock = QDockWidget("الخصائص", self)
        properties_dock.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        
        properties_widget = QWidget()
        layout = QVBoxLayout(properties_widget)
        
        # معلومات العنصر المختار
        self.selected_item_label = QLabel("لم يتم اختيار عنصر")
        self.selected_item_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(self.selected_item_label)
        
        # خصائص قابلة للتعديل
        props_group = QGroupBox("خصائص العنصر")
        props_layout = QVBoxLayout(props_group)
        
        # اللون
        color_layout = QHBoxLayout()
        color_layout.addWidget(QLabel("اللون:"))
        self.color_combo = QComboBox()
        self.color_combo.addItems(["أحمر", "أزرق", "أخضر", "أصفر", "بنفسجي"])
        self.color_combo.currentTextChanged.connect(self.update_properties)
        color_layout.addWidget(self.color_combo)
        props_layout.addLayout(color_layout)
        
        # الحجم
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("الحجم:"))
        self.size_slider = QSlider(Qt.Horizontal)
        self.size_slider.setRange(10, 100)
        self.size_slider.setValue(50)
        self.size_slider.valueChanged.connect(self.update_properties)
        size_layout.addWidget(self.size_slider)
        self.size_value_label = QLabel("50")
        size_layout.addWidget(self.size_value_label)
        props_layout.addLayout(size_layout)
        
        # مرئي
        self.visible_checkbox = QCheckBox("مرئي")
        self.visible_checkbox.setChecked(True)
        self.visible_checkbox.stateChanged.connect(self.update_properties)
        props_layout.addWidget(self.visible_checkbox)
        
        layout.addWidget(props_group)
        layout.addStretch()
        
        properties_dock.setWidget(properties_widget)
        self.addDockWidget(Qt.RightDockWidgetArea, properties_dock)
        
        # حفظ مرجع للرصيف
        self.properties_dock = properties_dock
        
    def create_tools_dock(self):
        """إنشاء رصيف الأدوات"""
        tools_dock = QDockWidget("الأدوات", self)
        tools_dock.setAllowedAreas(Qt.TopDockWidgetArea | Qt.BottomDockWidgetArea)
        
        tools_widget = QWidget()
        layout = QHBoxLayout(tools_widget)
        
        # أدوات الرسم
        drawing_group = QGroupBox("أدوات الرسم")
        drawing_layout = QVBoxLayout(drawing_group)
        
        draw_buttons = ["قلم", "فرشاة", "ممحاة", "خط", "مستطيل", "دائرة"]
        for button_text in draw_buttons:
            btn = QPushButton(button_text)
            btn.clicked.connect(lambda checked, text=button_text: self.tool_selected(text))
            btn.setStyleSheet("""
                QPushButton {
                    padding: 8px;
                    margin: 2px;
                    border-radius: 4px;
                    background-color: #3498db;
                    color: white;
                    border: none;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            drawing_layout.addWidget(btn)
            
        layout.addWidget(drawing_group)
        
        # أدوات التحرير
        editing_group = QGroupBox("أدوات التحرير")
        editing_layout = QVBoxLayout(editing_group)
        
        edit_buttons = ["نسخ", "قص", "لصق", "تراجع", "إعادة", "حذف"]
        for button_text in edit_buttons:
            btn = QPushButton(button_text)
            btn.clicked.connect(lambda checked, text=button_text: self.tool_selected(text))
            btn.setStyleSheet("""
                QPushButton {
                    padding: 8px;
                    margin: 2px;
                    border-radius: 4px;
                    background-color: #27ae60;
                    color: white;
                    border: none;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
            """)
            editing_layout.addWidget(btn)
            
        layout.addWidget(editing_group)
        
        # معلومات الأداة المختارة
        info_group = QGroupBox("معلومات الأداة")
        info_layout = QVBoxLayout(info_group)
        
        self.tool_info_label = QLabel("لم يتم اختيار أداة")
        self.tool_info_label.setWordWrap(True)
        self.tool_info_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 5px;
                border: 1px solid #dee2e6;
            }
        """)
        info_layout.addWidget(self.tool_info_label)
        
        layout.addWidget(info_group)
        
        tools_dock.setWidget(tools_widget)
        self.addDockWidget(Qt.BottomDockWidgetArea, tools_dock)
        
        # حفظ مرجع للرصيف
        self.tools_dock = tools_dock
        
    def create_info_dock(self):
        """إنشاء رصيف المعلومات"""
        info_dock = QDockWidget("معلومات النظام", self)
        info_dock.setAllowedAreas(Qt.TopDockWidgetArea | Qt.BottomDockWidgetArea)
        
        info_widget = QWidget()
        layout = QHBoxLayout(info_widget)
        
        # معلومات النظام
        self.system_info_label = QLabel()
        self.system_info_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #e8f5e8;
                border-radius: 5px;
                border: 1px solid #27ae60;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.system_info_label)
        
        # الوقت الحالي
        self.time_label = QLabel()
        self.time_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #e3f2fd;
                border-radius: 5px;
                border: 1px solid #2196f3;
                font-weight: bold;
                font-size: 14px;
                min-width: 200px;
            }
        """)
        layout.addWidget(self.time_label)
        
        info_dock.setWidget(info_widget)
        self.addDockWidget(Qt.TopDockWidgetArea, info_dock)
        
        # حفظ مرجع للرصيف
        self.info_dock = info_dock
        
        # تحديث المعلومات
        self.update_system_info()
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة العرض
        view_menu = menubar.addMenu("عرض")
        
        # إضافة إجراءات إظهار/إخفاء الأرصفة
        view_menu.addAction(self.files_dock.toggleViewAction())
        view_menu.addAction(self.properties_dock.toggleViewAction())
        view_menu.addAction(self.tools_dock.toggleViewAction())
        view_menu.addAction(self.info_dock.toggleViewAction())
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.statusBar().showMessage("جاهز - استخدم الأرصفة للتفاعل مع التطبيق")
        
    def on_file_selected(self, item, column):
        """عند اختيار ملف من الشجرة"""
        file_name = item.text(0)
        self.selected_item_label.setText(f"العنصر المختار: {file_name}")
        
        # تحديث منطقة النص الرئيسية
        self.main_text_area.append(f"\n✓ تم اختيار الملف: {file_name}")
        
        # تحديث شريط الحالة
        self.statusBar().showMessage(f"تم اختيار: {file_name}")
        
    def update_properties(self):
        """تحديث الخصائص"""
        color = self.color_combo.currentText()
        size = self.size_slider.value()
        visible = self.visible_checkbox.isChecked()
        
        self.size_value_label.setText(str(size))
        
        # تحديث منطقة النص الرئيسية
        self.main_text_area.append(f"\n⚙️ تم تحديث الخصائص: اللون={color}, الحجم={size}, مرئي={visible}")
        
    def tool_selected(self, tool_name):
        """عند اختيار أداة"""
        tool_descriptions = {
            "قلم": "أداة الرسم بالقلم - للرسم الحر",
            "فرشاة": "أداة الفرشاة - للرسم بخطوط عريضة",
            "ممحاة": "أداة المحو - لحذف أجزاء من الرسم",
            "خط": "أداة الخط - لرسم خطوط مستقيمة",
            "مستطيل": "أداة المستطيل - لرسم مستطيلات",
            "دائرة": "أداة الدائرة - لرسم دوائر وأشكال بيضاوية",
            "نسخ": "نسخ العنصر المختار",
            "قص": "قص العنصر المختار",
            "لصق": "لصق العنصر المنسوخ",
            "تراجع": "التراجع عن آخر عملية",
            "إعادة": "إعادة آخر عملية تم التراجع عنها",
            "حذف": "حذف العنصر المختار"
        }
        
        description = tool_descriptions.get(tool_name, "أداة غير معروفة")
        self.tool_info_label.setText(f"الأداة المختارة: {tool_name}\n\n{description}")
        
        # تحديث منطقة النص الرئيسية
        self.main_text_area.append(f"\n🔧 تم اختيار الأداة: {tool_name}")
        
        # تحديث شريط الحالة
        self.statusBar().showMessage(f"الأداة النشطة: {tool_name}")
        
    def update_system_info(self):
        """تحديث معلومات النظام"""
        import platform
        import psutil
        
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            info_text = f"""النظام: {platform.system()} {platform.release()}
المعالج: {cpu_percent}%
الذاكرة: {memory.percent}%
الأرصفة: {len(self.findChildren(QDockWidget))}"""
            
            self.system_info_label.setText(info_text)
        except:
            self.system_info_label.setText("معلومات النظام غير متوفرة")
            
    def update_time(self):
        """تحديث الوقت"""
        current_time = QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
        self.time_label.setText(f"الوقت: {current_time}")
        
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        self.timer.stop()
        event.accept()
