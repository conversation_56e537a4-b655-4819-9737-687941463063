#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نوافذ الحوار في PySide6
Dialog Windows in PySide6
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QLineEdit, QTextEdit, QMessageBox, QInputDialog, QFileDialog,
    QColorDialog, QCheckBox, QSpinBox, QComboBox, QDialogButtonBox
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QColor


class ModalDialog(QDialog):
    """حوار مودال - يمنع التفاعل مع النوافذ الأخرى"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("حوار مودال")
        self.setModal(True)  # جعل الحوار مودال
        self.setFixedSize(400, 300)
        
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # العنوان
        title = QLabel("حوار مودال (Modal Dialog)")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # الوصف
        description = QLabel("""
        الحوار المودال يمنع المستخدم من التفاعل مع النوافذ الأخرى
        حتى يتم إغلاقه. يستخدم عادة للرسائل المهمة أو
        طلب معلومات ضرورية من المستخدم.
        """)
        description.setWordWrap(True)
        description.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                padding: 15px;
                border-radius: 5px;
                border: 1px solid #bdc3c7;
                line-height: 1.5;
            }
        """)
        layout.addWidget(description)
        
        # حقول الإدخال
        input_layout = QVBoxLayout()
        
        input_layout.addWidget(QLabel("الاسم:"))
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسمك")
        input_layout.addWidget(self.name_input)
        
        input_layout.addWidget(QLabel("العمر:"))
        self.age_input = QSpinBox()
        self.age_input.setRange(1, 120)
        self.age_input.setValue(25)
        input_layout.addWidget(self.age_input)
        
        input_layout.addWidget(QLabel("المدينة:"))
        self.city_input = QComboBox()
        self.city_input.addItems(["الرياض", "جدة", "الدمام", "مكة", "المدينة"])
        input_layout.addWidget(self.city_input)
        
        layout.addLayout(input_layout)
        
        # أزرار التحكم
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept_dialog)
        button_box.rejected.connect(self.reject)
        
        # تخصيص الأزرار
        ok_button = button_box.button(QDialogButtonBox.Ok)
        ok_button.setText("موافق")
        cancel_button = button_box.button(QDialogButtonBox.Cancel)
        cancel_button.setText("إلغاء")
        
        layout.addWidget(button_box)
        
    def accept_dialog(self):
        """عند قبول الحوار"""
        name = self.name_input.text()
        age = self.age_input.value()
        city = self.city_input.currentText()
        
        if not name.strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الاسم")
            return
            
        result_msg = f"تم حفظ البيانات:\nالاسم: {name}\nالعمر: {age}\nالمدينة: {city}"
        QMessageBox.information(self, "نجح", result_msg)
        self.accept()


class ModelessDialog(QDialog):
    """حوار غير مودال - يسمح بالتفاعل مع النوافذ الأخرى"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("حوار غير مودال")
        self.setModal(False)  # جعل الحوار غير مودال
        self.resize(350, 400)
        
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # العنوان
        title = QLabel("حوار غير مودال (Modeless Dialog)")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #27ae60;
                padding: 10px;
                background-color: #d5f4e6;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # الوصف
        description = QLabel("""
        الحوار غير المودال يسمح للمستخدم بالتفاعل مع
        النوافذ الأخرى أثناء بقائه مفتوحاً. مفيد للأدوات
        والنوافذ المساعدة التي تحتاج للبقاء مرئية.
        """)
        description.setWordWrap(True)
        description.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                padding: 15px;
                border-radius: 5px;
                border: 1px solid #bdc3c7;
                line-height: 1.5;
            }
        """)
        layout.addWidget(description)
        
        # منطقة النشاط
        activity_label = QLabel("منطقة النشاط:")
        activity_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(activity_label)
        
        self.activity_text = QTextEdit()
        self.activity_text.setMaximumHeight(150)
        self.activity_text.setPlainText("يمكنك التفاعل مع النوافذ الأخرى أثناء بقاء هذا الحوار مفتوحاً...")
        layout.addWidget(self.activity_text)
        
        # عداد الوقت
        self.time_label = QLabel("الوقت المنقضي: 0 ثانية")
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setStyleSheet("""
            QLabel {
                background-color: #3498db;
                color: white;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.time_label)
        
        # بدء العداد
        self.elapsed_time = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_timer)
        self.timer.start(1000)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        add_text_btn = QPushButton("إضافة نص")
        add_text_btn.clicked.connect(self.add_text)
        add_text_btn.setStyleSheet(self.get_button_style("#3498db", "#2980b9"))
        
        clear_btn = QPushButton("مسح")
        clear_btn.clicked.connect(self.clear_text)
        clear_btn.setStyleSheet(self.get_button_style("#e74c3c", "#c0392b"))
        
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet(self.get_button_style("#95a5a6", "#7f8c8d"))
        
        buttons_layout.addWidget(add_text_btn)
        buttons_layout.addWidget(clear_btn)
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)
        
    def get_button_style(self, bg_color, hover_color):
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
        """
        
    def update_timer(self):
        """تحديث العداد"""
        self.elapsed_time += 1
        self.time_label.setText(f"الوقت المنقضي: {self.elapsed_time} ثانية")
        
    def add_text(self):
        """إضافة نص جديد"""
        current_text = self.activity_text.toPlainText()
        new_text = f"{current_text}\nتم إضافة نص في الثانية {self.elapsed_time}"
        self.activity_text.setPlainText(new_text)
        
        # التمرير إلى الأسفل
        cursor = self.activity_text.textCursor()
        cursor.movePosition(cursor.End)
        self.activity_text.setTextCursor(cursor)
        
    def clear_text(self):
        """مسح النص"""
        self.activity_text.clear()
        
    def closeEvent(self, event):
        """عند إغلاق الحوار"""
        self.timer.stop()
        event.accept()


class MessageBoxDemo:
    """عرض توضيحي لصناديق الرسائل"""
    
    def __init__(self, parent=None):
        self.parent = parent
        
    def show_all_message_boxes(self):
        """عرض جميع أنواع صناديق الرسائل"""
        # رسالة معلومات
        QMessageBox.information(self.parent, "معلومات", 
                              "هذه رسالة معلومات.\nتستخدم لعرض معلومات عامة للمستخدم.")
        
        # رسالة تحذير
        QMessageBox.warning(self.parent, "تحذير", 
                          "هذه رسالة تحذير.\nتستخدم لتنبيه المستخدم لشيء مهم.")
        
        # رسالة خطأ
        QMessageBox.critical(self.parent, "خطأ", 
                           "هذه رسالة خطأ.\nتستخدم لإعلام المستخدم بحدوث خطأ.")
        
        # رسالة سؤال
        reply = QMessageBox.question(self.parent, "سؤال", 
                                   "هل تريد المتابعة؟\nهذا مثال على رسالة سؤال.",
                                   QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)
        
        if reply == QMessageBox.Yes:
            result = "اخترت: نعم"
        elif reply == QMessageBox.No:
            result = "اخترت: لا"
        else:
            result = "اخترت: إلغاء"
            
        QMessageBox.information(self.parent, "النتيجة", result)


class InputDialogDemo:
    """عرض توضيحي لحوارات الإدخال"""
    
    def __init__(self, parent=None):
        self.parent = parent
        
    def show_input_dialogs(self):
        """عرض حوارات الإدخال المختلفة"""
        # إدخال نص
        text, ok = QInputDialog.getText(self.parent, "إدخال نص", "أدخل اسمك:")
        if ok and text:
            QMessageBox.information(self.parent, "النتيجة", f"الاسم المدخل: {text}")
        
        # إدخال رقم صحيح
        number, ok = QInputDialog.getInt(self.parent, "إدخال رقم", "أدخل عمرك:", 25, 1, 120, 1)
        if ok:
            QMessageBox.information(self.parent, "النتيجة", f"العمر المدخل: {number}")
        
        # إدخال رقم عشري
        double_num, ok = QInputDialog.getDouble(self.parent, "إدخال رقم عشري", "أدخل طولك (بالمتر):", 1.70, 0.5, 3.0, 2)
        if ok:
            QMessageBox.information(self.parent, "النتيجة", f"الطول المدخل: {double_num:.2f} متر")
        
        # اختيار من قائمة
        items = ["الرياض", "جدة", "الدمام", "مكة", "المدينة"]
        item, ok = QInputDialog.getItem(self.parent, "اختيار مدينة", "اختر مدينتك:", items, 0, False)
        if ok and item:
            QMessageBox.information(self.parent, "النتيجة", f"المدينة المختارة: {item}")


class FileDialogDemo:
    """عرض توضيحي لحوارات الملفات"""
    
    def __init__(self, parent=None):
        self.parent = parent
        
    def show_file_dialogs(self):
        """عرض حوارات الملفات المختلفة"""
        # فتح ملف واحد
        file_path, _ = QFileDialog.getOpenFileName(
            self.parent, "فتح ملف", "", 
            "ملفات النص (*.txt);;ملفات الصور (*.png *.jpg *.jpeg);;جميع الملفات (*)"
        )
        if file_path:
            QMessageBox.information(self.parent, "ملف مختار", f"تم اختيار:\n{file_path}")
        
        # فتح ملفات متعددة
        file_paths, _ = QFileDialog.getOpenFileNames(
            self.parent, "فتح ملفات متعددة", "", 
            "ملفات النص (*.txt);;جميع الملفات (*)"
        )
        if file_paths:
            files_text = "\n".join(file_paths)
            QMessageBox.information(self.parent, "ملفات مختارة", f"تم اختيار {len(file_paths)} ملف:\n{files_text}")
        
        # حفظ ملف
        save_path, _ = QFileDialog.getSaveFileName(
            self.parent, "حفظ ملف", "document.txt", 
            "ملفات النص (*.txt);;جميع الملفات (*)"
        )
        if save_path:
            QMessageBox.information(self.parent, "مسار الحفظ", f"سيتم الحفظ في:\n{save_path}")
        
        # اختيار مجلد
        folder_path = QFileDialog.getExistingDirectory(self.parent, "اختيار مجلد")
        if folder_path:
            QMessageBox.information(self.parent, "مجلد مختار", f"تم اختيار المجلد:\n{folder_path}")


class ColorDialogDemo:
    """عرض توضيحي لحوار الألوان"""
    
    def __init__(self, parent=None):
        self.parent = parent
        
    def show_color_dialog(self):
        """عرض حوار اختيار الألوان"""
        color = QColorDialog.getColor(QColor(255, 0, 0), self.parent, "اختيار لون")
        
        if color.isValid():
            color_info = f"""تم اختيار اللون:
            
الاسم: {color.name()}
RGB: ({color.red()}, {color.green()}, {color.blue()})
HSV: ({color.hue()}, {color.saturation()}, {color.value()})
الشفافية: {color.alpha()}"""
            
            # إنشاء رسالة مع لون الخلفية
            msg_box = QMessageBox(self.parent)
            msg_box.setWindowTitle("اللون المختار")
            msg_box.setText(color_info)
            msg_box.setStyleSheet(f"""
                QMessageBox {{
                    background-color: {color.name()};
                    color: {'white' if color.lightness() < 128 else 'black'};
                }}
                QMessageBox QLabel {{
                    color: {'white' if color.lightness() < 128 else 'black'};
                }}
            """)
            msg_box.exec()
