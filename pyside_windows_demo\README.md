# عرض توضيحي شامل لأنواع النوافذ في PySide6
# Comprehensive PySide6 Windows Types Demo

## نظرة عامة | Overview

هذا المشروع يقدم عرضاً توضيحياً شاملاً لجميع أنواع النوافذ المختلفة في PySide6، مع دعم كامل للغة العربية وواجهات مستخدم تفاعلية.

This project provides a comprehensive demonstration of all different window types in PySide6, with full Arabic language support and interactive user interfaces.

## أنواع النوافذ المدعومة | Supported Window Types

### 1. النافذة الأساسية | Basic Window (`basic_window.py`)
- نافذة QWidget بسيطة مع عناصر تفاعلية
- شريط تقدم وأزرار تحكم
- رسوم متحركة وتأثيرات بصرية
- تسجيل تفاعلات المستخدم

### 2. النافذة الرئيسية | Main Window (`main_window.py`)
- نافذة QMainWindow كاملة المواصفات
- قوائم وأشرطة أدوات
- شريط حالة ومحرر نصوص
- عمليات الملفات (فتح، حفظ، جديد)

### 3. نوافذ الحوار | Dialog Windows (`dialog_windows.py`)
- حوارات مودال وغير مودال
- حوارات النظام (رسائل، ألوان، ملفات)
- حوارات الإدخال المخصصة
- أمثلة تفاعلية متنوعة

### 4. النوافذ المنبثقة | Popup Windows (`popup_windows.py`)
- نوافذ منبثقة مخصصة مع رسوم متحركة
- تلميحات أدوات متقدمة
- نوافذ عائمة قابلة للتخصيص
- تأثيرات بصرية متطورة

### 5. النافذة بدون إطار | Frameless Window (`frameless_window.py`)
- نافذة بدون حدود النظام الافتراضية
- شريط عنوان مخصص بالكامل
- أزرار تحكم مخصصة (تصغير، تكبير، إغلاق)
- إمكانية السحب والتحريك
- تأثيرات الظل والشفافية

### 6. شاشة البداية | Splash Screen (`splash_screen.py`)
- شاشة تحميل مخصصة مع شريط تقدم
- رسوم متحركة أثناء التحميل
- محاكاة عملية تحميل التطبيق
- انتقال سلس للنافذة الرئيسية

### 7. نافذة الأرصفة | Dock Window (`dock_window.py`)
- نافذة رئيسية مع أرصفة قابلة للإرساء
- أرصفة للملفات والخصائص والأدوات
- إمكانية السحب والإفلات للأرصفة
- تجميع الأرصفة في تبويبات
- معلومات النظام في الوقت الفعلي

### 8. نافذة متعددة الوثائق | MDI Window (`mdi_window.py`)
- واجهة متعددة الوثائق (MDI)
- فتح عدة وثائق في نافذة واحدة
- عرض بتبويبات أو نوافذ منفصلة
- محرر نصوص متقدم لكل وثيقة
- إدارة شاملة للوثائق المتعددة

## المتطلبات | Requirements

```bash
pip install PySide6 psutil
```

## كيفية التشغيل | How to Run

```bash
cd pyside_windows_demo
python main.py
```

## هيكل المشروع | Project Structure

```
pyside_windows_demo/
├── main.py                 # النافذة الرئيسية للتشغيل
├── basic_window.py         # النافذة الأساسية
├── main_window.py          # النافذة الرئيسية
├── dialog_windows.py       # نوافذ الحوار
├── popup_windows.py        # النوافذ المنبثقة
├── frameless_window.py     # النافذة بدون إطار
├── splash_screen.py        # شاشة البداية
├── dock_window.py          # نافذة الأرصفة
├── mdi_window.py           # نافذة متعددة الوثائق
└── README.md               # هذا الملف
```

## الميزات الرئيسية | Key Features

### 🌐 دعم اللغة العربية
- واجهات مستخدم باللغة العربية
- دعم النصوص من اليمين إلى اليسار
- تعليقات وتوثيق شامل بالعربية

### 🎨 تصميم متطور
- واجهات مستخدم جذابة ومتجاوبة
- تأثيرات بصرية ورسوم متحركة
- أنماط CSS مخصصة لكل نوع نافذة

### 🔧 تفاعلية كاملة
- أمثلة تفاعلية لكل نوع نافذة
- إمكانية تجربة جميع الميزات
- ردود فعل فورية للمستخدم

### 📚 تعليمية
- شرح مفصل لكل نوع نافذة
- أمثلة عملية وقابلة للتطبيق
- كود مصدري واضح ومنظم

## استخدام المشروع | Using the Project

1. **تشغيل العرض التوضيحي**: شغل `main.py` لفتح النافذة الرئيسية
2. **استكشاف أنواع النوافذ**: انقر على الأزرار لفتح أنواع النوافذ المختلفة
3. **التفاعل مع الأمثلة**: جرب جميع الميزات والوظائف المتاحة
4. **دراسة الكود**: اطلع على الكود المصدري لفهم التطبيق

## الاستخدام التعليمي | Educational Use

هذا المشروع مثالي لـ:
- تعلم PySide6 وتطوير واجهات المستخدم
- فهم أنواع النوافذ المختلفة في Qt
- تطوير تطبيقات سطح المكتب بـ Python
- دراسة أفضل الممارسات في تصميم واجهات المستخدم

## المساهمة | Contributing

نرحب بالمساهمات لتحسين هذا المشروع:
- إضافة أنواع نوافذ جديدة
- تحسين التصميم والواجهات
- إصلاح الأخطاء وتحسين الأداء
- تحسين التوثيق والشرح

## الترخيص | License

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.

---

**ملاحظة**: هذا المشروع تم تطويره كعرض توضيحي شامل لأنواع النوافذ في PySide6 مع دعم كامل للغة العربية.

**Note**: This project was developed as a comprehensive demonstration of window types in PySide6 with full Arabic language support.
