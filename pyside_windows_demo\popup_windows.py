#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النوافذ المنبثقة في PySide6
Popup Windows in PySide6
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTextEdit, QFrame, QApplication
)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QRect, QEasingCurve
from PySide6.QtGui import QFont, QPalette


class PopupWindow(QWidget):
    """نافذة منبثقة مخصصة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("نافذة منبثقة")
        self.setWindowFlags(Qt.Popup | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedSize(300, 200)
        
        # تحديد موقع النافذة بالنسبة للوالد
        if parent:
            parent_rect = parent.geometry()
            x = parent_rect.x() + parent_rect.width() // 2 - self.width() // 2
            y = parent_rect.y() + parent_rect.height() // 2 - self.height() // 2
            self.move(x, y)
        
        self.setup_ui()
        self.setup_animation()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي مع حدود مستديرة
        main_frame = QFrame(self)
        main_frame.setGeometry(0, 0, self.width(), self.height())
        main_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(44, 62, 80, 240);
                border-radius: 15px;
                border: 2px solid #3498db;
            }
        """)
        
        layout = QVBoxLayout(main_frame)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        title = QLabel("نافذة منبثقة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: none;
                border: none;
                padding: 5px;
            }
        """)
        layout.addWidget(title)
        
        # الوصف
        description = QLabel("""
        هذه نافذة منبثقة تظهر فوق النوافذ الأخرى
        وتختفي عند النقر خارجها أو الضغط على Escape
        """)
        description.setWordWrap(True)
        description.setAlignment(Qt.AlignCenter)
        description.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 12px;
                background: none;
                border: none;
                line-height: 1.4;
                padding: 10px;
            }
        """)
        layout.addWidget(description)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        action_btn = QPushButton("إجراء")
        action_btn.clicked.connect(self.perform_action)
        action_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(action_btn)
        buttons_layout.addWidget(close_btn)
        layout.addLayout(buttons_layout)
        
    def setup_animation(self):
        """إعداد الرسوم المتحركة"""
        # رسوم متحركة للظهور
        self.show_animation = QPropertyAnimation(self, b"geometry")
        self.show_animation.setDuration(300)
        self.show_animation.setEasingCurve(QEasingCurve.OutBack)
        
        # رسوم متحركة للاختفاء
        self.hide_animation = QPropertyAnimation(self, b"geometry")
        self.hide_animation.setDuration(200)
        self.hide_animation.setEasingCurve(QEasingCurve.InBack)
        self.hide_animation.finished.connect(super().close)
        
    def show(self):
        """عرض النافذة مع رسوم متحركة"""
        super().show()
        
        # الحصول على الموقع النهائي
        final_rect = self.geometry()
        
        # البدء من حجم صغير
        start_rect = QRect(
            final_rect.x() + final_rect.width() // 2,
            final_rect.y() + final_rect.height() // 2,
            0, 0
        )
        
        self.setGeometry(start_rect)
        self.show_animation.setStartValue(start_rect)
        self.show_animation.setEndValue(final_rect)
        self.show_animation.start()
        
    def close(self):
        """إغلاق النافذة مع رسوم متحركة"""
        current_rect = self.geometry()
        end_rect = QRect(
            current_rect.x() + current_rect.width() // 2,
            current_rect.y() + current_rect.height() // 2,
            0, 0
        )
        
        self.hide_animation.setStartValue(current_rect)
        self.hide_animation.setEndValue(end_rect)
        self.hide_animation.start()
        
    def perform_action(self):
        """تنفيذ إجراء"""
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(self, "إجراء", "تم تنفيذ الإجراء بنجاح!")
        
    def keyPressEvent(self, event):
        """التعامل مع ضغط المفاتيح"""
        if event.key() == Qt.Key_Escape:
            self.close()
        super().keyPressEvent(event)


class ToolTipWindow(QWidget):
    """نافذة تلميح مخصصة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("نافذة التلميحات")
        self.setGeometry(300, 300, 400, 300)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # العنوان
        title = QLabel("نافذة التلميحات المخصصة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # الوصف
        description = QLabel("""
        هذه نافذة تعرض تلميحات مخصصة. مرر الماوس فوق الأزرار
        أدناه لرؤية تلميحات مختلفة الأنواع والأشكال.
        """)
        description.setWordWrap(True)
        description.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                padding: 15px;
                border-radius: 5px;
                border: 1px solid #bdc3c7;
                line-height: 1.5;
            }
        """)
        layout.addWidget(description)
        
        # أزرار مع تلميحات مختلفة
        buttons_layout = QVBoxLayout()
        
        # زر مع تلميح بسيط
        simple_btn = QPushButton("تلميح بسيط")
        simple_btn.setToolTip("هذا تلميح بسيط")
        simple_btn.setStyleSheet(self.get_button_style("#3498db", "#2980b9"))
        buttons_layout.addWidget(simple_btn)
        
        # زر مع تلميح غني
        rich_btn = QPushButton("تلميح غني")
        rich_btn.setToolTip("""
        <html>
        <head/>
        <body>
        <p><b>تلميح غني بـ HTML</b></p>
        <p>يمكن أن يحتوي على:</p>
        <ul>
        <li><font color="red">نص ملون</font></li>
        <li><b>نص عريض</b></li>
        <li><i>نص مائل</i></li>
        <li>قوائم وجداول</li>
        </ul>
        </body>
        </html>
        """)
        rich_btn.setStyleSheet(self.get_button_style("#27ae60", "#229954"))
        buttons_layout.addWidget(rich_btn)
        
        # زر مع تلميح طويل
        long_btn = QPushButton("تلميح طويل")
        long_btn.setToolTip("""
        هذا تلميح طويل جداً يوضح كيف يمكن للتلميحات أن تحتوي على
        نصوص طويلة ومفصلة. يمكن أن تشمل معلومات مفيدة حول
        وظيفة الزر أو العنصر، وتعليمات الاستخدام، وأي معلومات
        أخرى مهمة للمستخدم. التلميحات تظهر تلقائياً عند تمرير
        الماوس فوق العنصر وتختفي عند إبعاد الماوس.
        """)
        long_btn.setStyleSheet(self.get_button_style("#e74c3c", "#c0392b"))
        buttons_layout.addWidget(long_btn)
        
        # زر لعرض تلميح مخصص
        custom_btn = QPushButton("تلميح مخصص")
        custom_btn.clicked.connect(self.show_custom_tooltip)
        custom_btn.setStyleSheet(self.get_button_style("#9b59b6", "#8e44ad"))
        buttons_layout.addWidget(custom_btn)
        
        layout.addLayout(buttons_layout)
        
        # منطقة معلومات
        self.info_area = QTextEdit()
        self.info_area.setPlainText("مرر الماوس فوق الأزرار لرؤية التلميحات...")
        self.info_area.setMaximumHeight(80)
        self.info_area.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Courier New', monospace;
            }
        """)
        layout.addWidget(self.info_area)
        
    def get_button_style(self, bg_color, hover_color):
        """الحصول على نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {hover_color};
                transform: translateY(1px);
            }}
        """
        
    def show_custom_tooltip(self):
        """عرض تلميح مخصص"""
        # إنشاء نافذة تلميح مخصصة
        tooltip = CustomTooltip(self)
        
        # تحديد موقع التلميح
        button = self.sender()
        button_pos = button.mapToGlobal(button.rect().bottomLeft())
        tooltip.move(button_pos.x(), button_pos.y() + 5)
        
        tooltip.show()
        
        # إخفاء التلميح بعد 3 ثوان
        QTimer.singleShot(3000, tooltip.close)
        
        self.info_area.setPlainText("تم عرض تلميح مخصص!")


class CustomTooltip(QWidget):
    """تلميح مخصص"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.ToolTip | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedSize(250, 120)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = QFrame(self)
        main_frame.setGeometry(0, 0, self.width(), self.height())
        main_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(52, 73, 94, 230);
                border-radius: 10px;
                border: 2px solid #f39c12;
            }
        """)
        
        layout = QVBoxLayout(main_frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # أيقونة أو رمز
        icon_label = QLabel("💡")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                background: none;
                border: none;
            }
        """)
        layout.addWidget(icon_label)
        
        # النص
        text_label = QLabel("تلميح مخصص!")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: none;
                border: none;
            }
        """)
        layout.addWidget(text_label)
        
        # وصف
        desc_label = QLabel("هذا تلميح مخصص بتصميم خاص")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 11px;
                background: none;
                border: none;
            }
        """)
        layout.addWidget(desc_label)
