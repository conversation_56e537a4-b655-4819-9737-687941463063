#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية في PySide6
Main Window in PySide6
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QLabel, QPushButton, QMenuBar, QMenu,
    QToolBar, QStatusBar, QFileDialog,
    QMessageBox, QApplication, QSplitter
)
from PySide6.QtCore import Qt, QTimer, QDateTime
from PySide6.QtGui import QIcon, QKeySequence, QFont, QTextCursor, QAction


class CustomMainWindow(QMainWindow):
    """نافذة رئيسية مخصصة - QMainWindow"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نافذة رئيسية - QMainWindow")
        self.setGeometry(150, 150, 800, 600)
        
        # متغيرات الحالة
        self.file_path = None
        self.is_modified = False
        
        self.setup_ui()
        self.create_menus()
        self.create_toolbars()
        self.create_status_bar()
        
        # تحديث شريط الحالة كل ثانية
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_time)
        self.status_timer.start(1000)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # العنوان والوصف
        title = QLabel("النافذة الرئيسية (QMainWindow)")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        description = QLabel("""
        النافذة الرئيسية (QMainWindow) هي النوع الأكثر تطوراً من النوافذ في PySide6. تتضمن:
        • شريط القوائم (Menu Bar) - في الأعلى
        • شريط الأدوات (Tool Bar) - قابل للتخصيص والنقل
        • شريط الحالة (Status Bar) - في الأسفل
        • منطقة مركزية للمحتوى الرئيسي
        • إمكانية إضافة أرصفة (Dock Widgets) على الجوانب
        """)
        description.setWordWrap(True)
        description.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                padding: 15px;
                border-radius: 5px;
                border: 1px solid #bdc3c7;
                line-height: 1.6;
                color: #34495e;
            }
        """)
        layout.addWidget(description)
        
        # منطقة المحتوى الرئيسي مع تقسيم
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - محرر النصوص
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        editor_label = QLabel("محرر النصوص")
        editor_label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 5px;")
        left_layout.addWidget(editor_label)
        
        self.text_editor = QTextEdit()
        self.text_editor.setPlaceholderText("اكتب النص هنا...")
        self.text_editor.textChanged.connect(self.on_text_changed)
        self.text_editor.setStyleSheet("""
            QTextEdit {
                border: 2px solid #3498db;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border-color: #2980b9;
            }
        """)
        left_layout.addWidget(self.text_editor)
        
        # الجانب الأيمن - منطقة المعلومات
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        info_label = QLabel("معلومات النافذة")
        info_label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 5px;")
        right_layout.addWidget(info_label)
        
        self.info_display = QTextEdit()
        self.info_display.setReadOnly(True)
        self.info_display.setMaximumWidth(300)
        self.info_display.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        right_layout.addWidget(self.info_display)
        
        # أزرار التحكم
        buttons_widget = QWidget()
        buttons_layout = QHBoxLayout(buttons_widget)
        
        clear_btn = QPushButton("مسح النص")
        clear_btn.clicked.connect(self.clear_text)
        clear_btn.setStyleSheet(self.get_button_style("#e74c3c", "#c0392b"))
        
        sample_btn = QPushButton("نص تجريبي")
        sample_btn.clicked.connect(self.insert_sample_text)
        sample_btn.setStyleSheet(self.get_button_style("#3498db", "#2980b9"))
        
        word_count_btn = QPushButton("عدد الكلمات")
        word_count_btn.clicked.connect(self.show_word_count)
        word_count_btn.setStyleSheet(self.get_button_style("#27ae60", "#229954"))
        
        buttons_layout.addWidget(clear_btn)
        buttons_layout.addWidget(sample_btn)
        buttons_layout.addWidget(word_count_btn)
        buttons_layout.addStretch()
        
        right_layout.addWidget(buttons_widget)
        
        # إضافة الأجزاء إلى المقسم
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([500, 300])
        
        layout.addWidget(splitter)
        
        # تحديث المعلومات الأولية
        self.update_window_info()
        
    def create_menus(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        new_action = QAction("جديد", self)
        new_action.setShortcut(QKeySequence.New)
        new_action.triggered.connect(self.new_file)
        file_menu.addAction(new_action)
        
        open_action = QAction("فتح", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)
        
        save_action = QAction("حفظ", self)
        save_action.setShortcut(QKeySequence.Save)
        save_action.triggered.connect(self.save_file)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة التحرير
        edit_menu = menubar.addMenu("تحرير")
        
        copy_action = QAction("نسخ", self)
        copy_action.setShortcut(QKeySequence.Copy)
        copy_action.triggered.connect(self.text_editor.copy)
        edit_menu.addAction(copy_action)
        
        paste_action = QAction("لصق", self)
        paste_action.setShortcut(QKeySequence.Paste)
        paste_action.triggered.connect(self.text_editor.paste)
        edit_menu.addAction(paste_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("عرض")
        
        fullscreen_action = QAction("ملء الشاشة", self)
        fullscreen_action.setShortcut(QKeySequence.FullScreen)
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_toolbars(self):
        """إنشاء شريط الأدوات"""
        toolbar = QToolBar("الأدوات الرئيسية")
        self.addToolBar(toolbar)
        
        # أدوات الملف
        new_action = QAction("جديد", self)
        new_action.triggered.connect(self.new_file)
        toolbar.addAction(new_action)
        
        open_action = QAction("فتح", self)
        open_action.triggered.connect(self.open_file)
        toolbar.addAction(open_action)
        
        save_action = QAction("حفظ", self)
        save_action.triggered.connect(self.save_file)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # أدوات التحرير
        copy_action = QAction("نسخ", self)
        copy_action.triggered.connect(self.text_editor.copy)
        toolbar.addAction(copy_action)
        
        paste_action = QAction("لصق", self)
        paste_action.triggered.connect(self.text_editor.paste)
        toolbar.addAction(paste_action)
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # رسالة دائمة
        self.status_bar.showMessage("جاهز")
        
        # عنصر دائم للوقت
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        
        # عنصر دائم لعدد الكلمات
        self.word_count_label = QLabel("الكلمات: 0")
        self.status_bar.addPermanentWidget(self.word_count_label)
        
    def get_button_style(self, bg_color, hover_color):
        """الحصول على نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {hover_color};
                transform: translateY(1px);
            }}
        """
    
    def update_window_info(self):
        """تحديث معلومات النافذة"""
        info = f"""معلومات النافذة الرئيسية:

النوع: QMainWindow
العنوان: {self.windowTitle()}
الحجم: {self.width()} × {self.height()}
الموقع: ({self.x()}, {self.y()})

المكونات:
✓ شريط القوائم
✓ شريط الأدوات  
✓ شريط الحالة
✓ منطقة مركزية
✓ اختصارات لوحة المفاتيح

الحالة: {'معدل' if self.is_modified else 'غير معدل'}
الملف: {self.file_path or 'غير محفوظ'}
"""
        self.info_display.setPlainText(info)
    
    def update_status_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = QDateTime.currentDateTime().toString("hh:mm:ss")
        self.time_label.setText(f"الوقت: {current_time}")
    
    def on_text_changed(self):
        """عند تغيير النص"""
        self.is_modified = True
        word_count = len(self.text_editor.toPlainText().split())
        self.word_count_label.setText(f"الكلمات: {word_count}")
        self.update_window_info()
    
    def clear_text(self):
        """مسح النص"""
        self.text_editor.clear()
        self.status_bar.showMessage("تم مسح النص", 2000)
    
    def insert_sample_text(self):
        """إدراج نص تجريبي"""
        sample_text = """مرحباً بك في النافذة الرئيسية!

هذا نص تجريبي يوضح إمكانيات النافذة الرئيسية في PySide6.

يمكنك:
• كتابة وتحرير النصوص
• استخدام القوائم وشريط الأدوات
• مراقبة الحالة في شريط الحالة
• حفظ وفتح الملفات
• استخدام اختصارات لوحة المفاتيح

جرب الميزات المختلفة واستكشف إمكانيات النافذة!"""
        
        self.text_editor.setPlainText(sample_text)
        self.status_bar.showMessage("تم إدراج النص التجريبي", 2000)
    
    def show_word_count(self):
        """عرض عدد الكلمات"""
        text = self.text_editor.toPlainText()
        word_count = len(text.split())
        char_count = len(text)
        
        QMessageBox.information(self, "إحصائيات النص", 
                              f"عدد الكلمات: {word_count}\nعدد الأحرف: {char_count}")
    
    def new_file(self):
        """ملف جديد"""
        if self.is_modified:
            reply = QMessageBox.question(self, "ملف جديد", 
                                       "هل تريد حفظ التغييرات؟",
                                       QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)
            if reply == QMessageBox.Yes:
                self.save_file()
            elif reply == QMessageBox.Cancel:
                return
        
        self.text_editor.clear()
        self.file_path = None
        self.is_modified = False
        self.update_window_info()
        self.status_bar.showMessage("ملف جديد", 2000)
    
    def open_file(self):
        """فتح ملف"""
        file_path, _ = QFileDialog.getOpenFileName(self, "فتح ملف", "", "ملفات النص (*.txt);;جميع الملفات (*)")
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                    self.text_editor.setPlainText(content)
                    self.file_path = file_path
                    self.is_modified = False
                    self.update_window_info()
                    self.status_bar.showMessage(f"تم فتح: {file_path}", 3000)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"لا يمكن فتح الملف:\n{str(e)}")
    
    def save_file(self):
        """حفظ الملف"""
        if not self.file_path:
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ الملف", "", "ملفات النص (*.txt);;جميع الملفات (*)")
            if not file_path:
                return
            self.file_path = file_path
        
        try:
            with open(self.file_path, 'w', encoding='utf-8') as file:
                file.write(self.text_editor.toPlainText())
                self.is_modified = False
                self.update_window_info()
                self.status_bar.showMessage(f"تم الحفظ: {self.file_path}", 3000)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"لا يمكن حفظ الملف:\n{str(e)}")
    
    def toggle_fullscreen(self):
        """تبديل ملء الشاشة"""
        if self.isFullScreen():
            self.showNormal()
            self.status_bar.showMessage("تم الخروج من ملء الشاشة", 2000)
        else:
            self.showFullScreen()
            self.status_bar.showMessage("تم تفعيل ملء الشاشة", 2000)
    
    def show_about(self):
        """عرض معلومات حول التطبيق"""
        QMessageBox.about(self, "حول التطبيق", 
                         """عرض توضيحي للنافذة الرئيسية

هذا مثال على النافذة الرئيسية في PySide6
يوضح الميزات الأساسية مثل:
• شريط القوائم والأدوات
• شريط الحالة
• التعامل مع الملفات
• اختصارات لوحة المفاتيح

تطوير: عرض PySide6 التوضيحي""")
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        if self.is_modified:
            reply = QMessageBox.question(self, "إغلاق النافذة", 
                                       "هل تريد حفظ التغييرات قبل الإغلاق؟",
                                       QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)
            if reply == QMessageBox.Yes:
                self.save_file()
                event.accept()
            elif reply == QMessageBox.No:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
