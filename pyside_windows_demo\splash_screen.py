#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة البداية في PySide6
Splash Screen in PySide6
"""

from PySide6.QtWidgets import (
    QSplashScreen, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QProgressBar, QApplication
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QPixmap, QFont, QPainter, QColor, QBrush


class LoadingWorker(QThread):
    """عامل التحميل في خيط منفصل"""
    progress_updated = Signal(int, str)
    finished = Signal()
    
    def run(self):
        """تشغيل عملية التحميل المحاكاة"""
        tasks = [
            "تحميل المكتبات...",
            "تهيئة قاعدة البيانات...",
            "تحميل الإعدادات...",
            "تحميل واجهة المستخدم...",
            "تحميل الموارد...",
            "التحقق من التحديثات...",
            "تهيئة النظام...",
            "اكتمال التحميل!"
        ]
        
        for i, task in enumerate(tasks):
            self.progress_updated.emit((i + 1) * 12, task)
            self.msleep(800)  # محاكاة وقت التحميل
            
        self.finished.emit()


class CustomSplashScreen(QSplashScreen):
    """شاشة بداية مخصصة"""
    
    def __init__(self):
        # إنشاء صورة مخصصة لشاشة البداية
        pixmap = self.create_splash_pixmap()
        super().__init__(pixmap)
        
        self.setWindowFlags(Qt.SplashScreen | Qt.FramelessWindowHint)
        
        # متغيرات التحكم
        self.progress_value = 0
        self.status_text = "جاري التحميل..."
        
        # إعداد عامل التحميل
        self.loading_worker = LoadingWorker()
        self.loading_worker.progress_updated.connect(self.update_progress)
        self.loading_worker.finished.connect(self.loading_finished)
        
    def create_splash_pixmap(self):
        """إنشاء صورة شاشة البداية"""
        pixmap = QPixmap(500, 300)
        pixmap.fill(QColor(44, 62, 80))
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # الخلفية المتدرجة
        gradient = QBrush(QColor(52, 152, 219))
        painter.setBrush(gradient)
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(0, 0, 500, 300, 15, 15)
        
        # العنوان الرئيسي
        painter.setPen(QColor(255, 255, 255))
        title_font = QFont("Arial", 24, QFont.Bold)
        painter.setFont(title_font)
        painter.drawText(50, 80, "عرض PySide6 التوضيحي")
        
        # العنوان الفرعي
        subtitle_font = QFont("Arial", 14)
        painter.setFont(subtitle_font)
        painter.drawText(50, 110, "أنواع النوافذ المختلفة")
        
        # رسم شعار أو أيقونة
        painter.setBrush(QBrush(QColor(241, 196, 15)))
        painter.drawEllipse(350, 50, 100, 100)
        
        # نص داخل الدائرة
        painter.setPen(QColor(44, 62, 80))
        icon_font = QFont("Arial", 16, QFont.Bold)
        painter.setFont(icon_font)
        painter.drawText(380, 105, "PySide6")
        
        painter.end()
        return pixmap
        
    def start_loading(self):
        """بدء عملية التحميل"""
        self.loading_worker.start()
        
    def update_progress(self, value, text):
        """تحديث شريط التقدم والنص"""
        self.progress_value = value
        self.status_text = text
        self.repaint()
        
    def loading_finished(self):
        """عند انتهاء التحميل"""
        QTimer.singleShot(1000, self.close)
        
    def drawContents(self, painter):
        """رسم المحتويات على شاشة البداية"""
        painter.setRenderHint(QPainter.Antialiasing)
        
        # رسم شريط التقدم
        progress_rect = self.rect()
        progress_rect.setTop(progress_rect.height() - 60)
        progress_rect.setBottom(progress_rect.height() - 40)
        progress_rect.setLeft(50)
        progress_rect.setRight(progress_rect.width() - 50)
        
        # خلفية شريط التقدم
        painter.setBrush(QBrush(QColor(189, 195, 199)))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(progress_rect, 10, 10)
        
        # شريط التقدم المملوء
        if self.progress_value > 0:
            filled_width = int((progress_rect.width() * self.progress_value) / 100)
            filled_rect = progress_rect
            filled_rect.setWidth(filled_width)
            
            painter.setBrush(QBrush(QColor(46, 204, 113)))
            painter.drawRoundedRect(filled_rect, 10, 10)
        
        # نص الحالة
        painter.setPen(QColor(255, 255, 255))
        status_font = QFont("Arial", 12)
        painter.setFont(status_font)
        
        text_rect = self.rect()
        text_rect.setTop(text_rect.height() - 35)
        text_rect.setLeft(50)
        text_rect.setRight(text_rect.width() - 50)
        
        painter.drawText(text_rect, Qt.AlignLeft | Qt.AlignVCenter, self.status_text)
        
        # نسبة التقدم
        progress_text = f"{self.progress_value}%"
        painter.drawText(text_rect, Qt.AlignRight | Qt.AlignVCenter, progress_text)


class SplashScreenDemo:
    """عرض توضيحي لشاشة البداية"""
    
    def __init__(self):
        self.splash = None
        
    def show_splash(self):
        """عرض شاشة البداية"""
        self.splash = CustomSplashScreen()
        self.splash.show()
        
        # بدء التحميل بعد عرض الشاشة
        QTimer.singleShot(500, self.splash.start_loading)
        
        # عرض نافذة النتيجة بعد انتهاء التحميل
        self.splash.loading_worker.finished.connect(self.show_result_window)
        
    def show_result_window(self):
        """عرض نافذة النتيجة بعد انتهاء التحميل"""
        QTimer.singleShot(1500, self.create_result_window)
        
    def create_result_window(self):
        """إنشاء نافذة النتيجة"""
        self.result_window = SplashResultWindow()
        self.result_window.show()


class SplashResultWindow(QWidget):
    """نافذة النتيجة بعد شاشة البداية"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نتيجة شاشة البداية")
        self.setGeometry(200, 200, 450, 350)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # العنوان
        title = QLabel("تم تحميل التطبيق بنجاح! 🎉")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #27ae60;
                padding: 20px;
                background-color: #d5f4e6;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # الوصف
        description = QLabel("""
        شاشة البداية (Splash Screen) تُستخدم لعرض معلومات التطبيق
        أثناء تحميله. خصائصها:
        
        ✓ تظهر قبل النافذة الرئيسية
        ✓ تعرض شعار أو معلومات التطبيق
        ✓ تحتوي على شريط تقدم للتحميل
        ✓ تختفي تلقائياً بعد انتهاء التحميل
        ✓ تحسن تجربة المستخدم أثناء بدء التطبيق
        ✓ يمكن تخصيصها بالكامل
        
        في هذا المثال، تم محاكاة عملية تحميل التطبيق
        مع عرض مراحل التحميل المختلفة.
        """)
        description.setWordWrap(True)
        description.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                padding: 20px;
                border-radius: 8px;
                border: 1px solid #bdc3c7;
                line-height: 1.6;
                color: #34495e;
            }
        """)
        layout.addWidget(description)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        show_again_btn = QPushButton("عرض شاشة البداية مرة أخرى")
        show_again_btn.clicked.connect(self.show_splash_again)
        show_again_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addWidget(show_again_btn)
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)
        
    def show_splash_again(self):
        """عرض شاشة البداية مرة أخرى"""
        demo = SplashScreenDemo()
        demo.show_splash()
