#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة بدون إطار في PySide6
Frameless Window in PySide6
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QFrame, QGraphicsDropShadowEffect
)
from PySide6.QtCore import Qt, QPoint
from PySide6.QtGui import QFont, QColor


class FramelessWindow(QWidget):
    """نافذة بدون إطار مع إمكانية السحب والتحريك"""
    
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedSize(500, 400)
        
        # متغيرات السحب
        self.drag_position = QPoint()
        
        self.setup_ui()
        self.add_shadow_effect()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        self.main_frame = QFrame(self)
        self.main_frame.setGeometry(10, 10, self.width() - 20, self.height() - 20)
        self.main_frame.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border-radius: 15px;
                border: 2px solid #3498db;
            }
        """)
        
        layout = QVBoxLayout(self.main_frame)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # شريط العنوان المخصص
        title_bar = self.create_title_bar()
        layout.addWidget(title_bar)
        
        # المحتوى الرئيسي
        content_area = self.create_content_area()
        layout.addWidget(content_area)
        
    def create_title_bar(self):
        """إنشاء شريط العنوان المخصص"""
        title_bar = QFrame()
        title_bar.setFixedHeight(50)
        title_bar.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-top-left-radius: 13px;
                border-top-right-radius: 13px;
                border-bottom: 1px solid #3498db;
            }
        """)
        
        layout = QHBoxLayout(title_bar)
        layout.setContentsMargins(15, 0, 15, 0)
        
        # العنوان
        title_label = QLabel("نافذة بدون إطار")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: none;
                border: none;
            }
        """)
        layout.addWidget(title_label)
        
        layout.addStretch()
        
        # أزرار التحكم
        minimize_btn = QPushButton("−")
        minimize_btn.setFixedSize(30, 30)
        minimize_btn.clicked.connect(self.showMinimized)
        minimize_btn.setStyleSheet(self.get_control_button_style("#f39c12"))
        
        maximize_btn = QPushButton("□")
        maximize_btn.setFixedSize(30, 30)
        maximize_btn.clicked.connect(self.toggle_maximize)
        maximize_btn.setStyleSheet(self.get_control_button_style("#27ae60"))
        
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet(self.get_control_button_style("#e74c3c"))
        
        layout.addWidget(minimize_btn)
        layout.addWidget(maximize_btn)
        layout.addWidget(close_btn)
        
        # جعل شريط العنوان قابل للسحب
        title_bar.mousePressEvent = self.title_bar_mouse_press
        title_bar.mouseMoveEvent = self.title_bar_mouse_move
        
        return title_bar
        
    def create_content_area(self):
        """إنشاء منطقة المحتوى"""
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border-bottom-left-radius: 13px;
                border-bottom-right-radius: 13px;
            }
        """)
        
        layout = QVBoxLayout(content_frame)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        title = QLabel("نافذة بدون إطار (Frameless Window)")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: white;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # الوصف
        description = QLabel("""
        النافذة بدون إطار تتيح تحكماً كاملاً في تصميم النافذة:
        
        ✓ لا توجد حدود نظام التشغيل الافتراضية
        ✓ شريط عنوان مخصص بالكامل
        ✓ أزرار تحكم مخصصة (تصغير، تكبير، إغلاق)
        ✓ إمكانية السحب والتحريك
        ✓ تصميم مرن وقابل للتخصيص
        ✓ تأثيرات بصرية متقدمة (ظلال، شفافية)
        
        يمكنك سحب النافذة من شريط العنوان لتحريكها!
        """)
        description.setWordWrap(True)
        description.setStyleSheet("""
            QLabel {
                background-color: white;
                padding: 20px;
                border-radius: 8px;
                border: 1px solid #bdc3c7;
                line-height: 1.6;
                color: #34495e;
            }
        """)
        layout.addWidget(description)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        opacity_btn = QPushButton("تغيير الشفافية")
        opacity_btn.clicked.connect(self.toggle_opacity)
        opacity_btn.setStyleSheet(self.get_action_button_style("#9b59b6", "#8e44ad"))
        
        resize_btn = QPushButton("تغيير الحجم")
        resize_btn.clicked.connect(self.change_size)
        resize_btn.setStyleSheet(self.get_action_button_style("#3498db", "#2980b9"))
        
        center_btn = QPushButton("توسيط النافذة")
        center_btn.clicked.connect(self.center_window)
        center_btn.setStyleSheet(self.get_action_button_style("#27ae60", "#229954"))
        
        actions_layout.addWidget(opacity_btn)
        actions_layout.addWidget(resize_btn)
        actions_layout.addWidget(center_btn)
        
        layout.addLayout(actions_layout)
        
        return content_frame
        
    def get_control_button_style(self, color):
        """نمط أزرار التحكم"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 15px;
                font-weight: bold;
                font-size: 16px;
            }}
            QPushButton:hover {{
                background-color: rgba(255, 255, 255, 50);
            }}
            QPushButton:pressed {{
                background-color: rgba(0, 0, 0, 50);
            }}
        """
        
    def get_action_button_style(self, bg_color, hover_color):
        """نمط أزرار الإجراءات"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {hover_color};
                transform: translateY(1px);
            }}
        """
        
    def add_shadow_effect(self):
        """إضافة تأثير الظل"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 100))
        self.main_frame.setGraphicsEffect(shadow)
        
    def title_bar_mouse_press(self, event):
        """عند الضغط على شريط العنوان"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
            
    def title_bar_mouse_move(self, event):
        """عند تحريك الماوس على شريط العنوان"""
        if event.buttons() == Qt.LeftButton and not self.drag_position.isNull():
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
            
    def toggle_maximize(self):
        """تبديل حالة التكبير"""
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()
            
    def toggle_opacity(self):
        """تبديل الشفافية"""
        current_opacity = self.windowOpacity()
        if current_opacity == 1.0:
            self.setWindowOpacity(0.8)
        elif current_opacity == 0.8:
            self.setWindowOpacity(0.6)
        else:
            self.setWindowOpacity(1.0)
            
    def change_size(self):
        """تغيير حجم النافذة"""
        current_size = self.size()
        if current_size.width() == 500:
            self.resize(600, 500)
        elif current_size.width() == 600:
            self.resize(400, 300)
        else:
            self.resize(500, 400)
            
        # إعادة تعيين حجم الإطار الرئيسي
        self.main_frame.setGeometry(10, 10, self.width() - 20, self.height() - 20)
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        from PySide6.QtWidgets import QApplication
        screen = QApplication.primaryScreen().geometry()
        window_geometry = self.frameGeometry()
        center_point = screen.center()
        window_geometry.moveCenter(center_point)
        self.move(window_geometry.topLeft())
        
    def keyPressEvent(self, event):
        """التعامل مع ضغط المفاتيح"""
        if event.key() == Qt.Key_Escape:
            self.close()
        elif event.key() == Qt.Key_F11:
            self.toggle_maximize()
        super().keyPressEvent(event)
