#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة متعددة الوثائق في PySide6
MDI (Multiple Document Interface) Window in PySide6
"""

from PySide6.QtWidgets import (
    QMainWindow, QMdiArea, QMdiSubWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QTextEdit, QLabel, QPushButton, QMenuBar, QMenu,
    QStatusBar, QToolBar, QMessageBox, QInputDialog,
    QColorDialog, QFontDialog, QFileDialog
)
from PySide6.QtCore import Qt, QTimer, QDateTime
from PySide6.QtGui import QFont, QColor, QTextCursor, QIcon, QAction


class MDIChildWindow(QWidget):
    """نافذة فرعية في MDI"""
    
    def __init__(self, title="وثيقة جديدة", content=""):
        super().__init__()
        self.setWindowTitle(title)
        self.document_modified = False
        
        self.setup_ui()
        if content:
            self.text_edit.setPlainText(content)
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # شريط معلومات الوثيقة
        info_bar = QWidget()
        info_bar.setStyleSheet("""
            QWidget {
                background-color: #ecf0f1;
                border-radius: 5px;
                padding: 5px;
            }
        """)
        info_layout = QHBoxLayout(info_bar)
        info_layout.setContentsMargins(10, 5, 10, 5)
        
        self.doc_info_label = QLabel(f"الوثيقة: {self.windowTitle()}")
        self.doc_info_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        info_layout.addWidget(self.doc_info_label)
        
        info_layout.addStretch()
        
        self.word_count_label = QLabel("الكلمات: 0")
        self.word_count_label.setStyleSheet("color: #7f8c8d;")
        info_layout.addWidget(self.word_count_label)
        
        layout.addWidget(info_bar)
        
        # محرر النصوص
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("ابدأ الكتابة هنا...")
        self.text_edit.textChanged.connect(self.on_text_changed)
        self.text_edit.setStyleSheet("""
            QTextEdit {
                border: 2px solid #3498db;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
                line-height: 1.4;
                background-color: white;
            }
            QTextEdit:focus {
                border-color: #2980b9;
            }
        """)
        layout.addWidget(self.text_edit)
        
        # شريط أدوات سريع
        tools_bar = QWidget()
        tools_layout = QHBoxLayout(tools_bar)
        tools_layout.setContentsMargins(5, 5, 5, 5)
        
        # أزرار التنسيق
        bold_btn = QPushButton("عريض")
        bold_btn.clicked.connect(self.toggle_bold)
        bold_btn.setStyleSheet(self.get_tool_button_style("#e74c3c"))
        
        italic_btn = QPushButton("مائل")
        italic_btn.clicked.connect(self.toggle_italic)
        italic_btn.setStyleSheet(self.get_tool_button_style("#f39c12"))
        
        color_btn = QPushButton("لون")
        color_btn.clicked.connect(self.change_text_color)
        color_btn.setStyleSheet(self.get_tool_button_style("#9b59b6"))
        
        clear_btn = QPushButton("مسح")
        clear_btn.clicked.connect(self.clear_text)
        clear_btn.setStyleSheet(self.get_tool_button_style("#95a5a6"))
        
        tools_layout.addWidget(bold_btn)
        tools_layout.addWidget(italic_btn)
        tools_layout.addWidget(color_btn)
        tools_layout.addWidget(clear_btn)
        tools_layout.addStretch()
        
        layout.addWidget(tools_bar)
        
    def get_tool_button_style(self, color):
        """نمط أزرار الأدوات"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
                margin: 2px;
            }}
            QPushButton:hover {{
                opacity: 0.8;
            }}
        """
        
    def on_text_changed(self):
        """عند تغيير النص"""
        self.document_modified = True
        
        # تحديث عدد الكلمات
        text = self.text_edit.toPlainText()
        word_count = len(text.split()) if text.strip() else 0
        self.word_count_label.setText(f"الكلمات: {word_count}")
        
        # تحديث عنوان النافذة
        title = self.windowTitle()
        if not title.endswith("*"):
            self.setWindowTitle(title + "*")
            
    def toggle_bold(self):
        """تبديل النص العريض"""
        cursor = self.text_edit.textCursor()
        format = cursor.charFormat()
        if format.fontWeight() == QFont.Bold:
            format.setFontWeight(QFont.Normal)
        else:
            format.setFontWeight(QFont.Bold)
        cursor.setCharFormat(format)
        
    def toggle_italic(self):
        """تبديل النص المائل"""
        cursor = self.text_edit.textCursor()
        format = cursor.charFormat()
        format.setFontItalic(not format.fontItalic())
        cursor.setCharFormat(format)
        
    def change_text_color(self):
        """تغيير لون النص"""
        color = QColorDialog.getColor(Qt.black, self)
        if color.isValid():
            cursor = self.text_edit.textCursor()
            format = cursor.charFormat()
            format.setForeground(color)
            cursor.setCharFormat(format)
            
    def clear_text(self):
        """مسح النص"""
        self.text_edit.clear()
        
    def get_content(self):
        """الحصول على محتوى الوثيقة"""
        return self.text_edit.toPlainText()
        
    def set_content(self, content):
        """تعيين محتوى الوثيقة"""
        self.text_edit.setPlainText(content)
        self.document_modified = False
        title = self.windowTitle().rstrip("*")
        self.setWindowTitle(title)


class MDIWindowDemo(QMainWindow):
    """نافذة رئيسية متعددة الوثائق"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نافذة متعددة الوثائق - MDI")
        self.setGeometry(100, 100, 1000, 700)
        
        self.document_counter = 1
        
        self.setup_ui()
        self.create_menus()
        self.create_toolbars()
        self.create_status_bar()
        
        # إنشاء وثيقة افتراضية
        self.new_document()
        
        # تحديث شريط الحالة
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_status)
        self.timer.start(1000)
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # منطقة MDI
        self.mdi_area = QMdiArea()
        self.mdi_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.mdi_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.mdi_area.setViewMode(QMdiArea.TabbedView)  # عرض بتبويبات
        self.mdi_area.setTabsClosable(True)
        self.mdi_area.setTabsMovable(True)
        
        # تخصيص مظهر MDI
        self.mdi_area.setStyleSheet("""
            QMdiArea {
                background-color: #f5f5f5;
                border: 1px solid #bdc3c7;
            }
            QMdiSubWindow {
                border: 2px solid #3498db;
                border-radius: 8px;
            }
            QMdiSubWindow::title {
                background-color: #3498db;
                color: white;
                padding: 5px;
                font-weight: bold;
            }
        """)
        
        self.setCentralWidget(self.mdi_area)
        
    def create_menus(self):
        """إنشاء القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        new_action = QAction("جديد", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_document)
        file_menu.addAction(new_action)
        
        open_action = QAction("فتح", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.open_document)
        file_menu.addAction(open_action)
        
        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_document)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        close_action = QAction("إغلاق الوثيقة", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close_document)
        file_menu.addAction(close_action)
        
        close_all_action = QAction("إغلاق الكل", self)
        close_all_action.triggered.connect(self.close_all_documents)
        file_menu.addAction(close_all_action)
        
        # قائمة النوافذ
        window_menu = menubar.addMenu("نوافذ")
        
        cascade_action = QAction("ترتيب متدرج", self)
        cascade_action.triggered.connect(self.mdi_area.cascadeSubWindows)
        window_menu.addAction(cascade_action)
        
        tile_action = QAction("ترتيب متجانب", self)
        tile_action.triggered.connect(self.mdi_area.tileSubWindows)
        window_menu.addAction(tile_action)
        
        window_menu.addSeparator()
        
        tab_view_action = QAction("عرض بتبويبات", self)
        tab_view_action.setCheckable(True)
        tab_view_action.setChecked(True)
        tab_view_action.triggered.connect(self.toggle_view_mode)
        window_menu.addAction(tab_view_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول MDI", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_toolbars(self):
        """إنشاء أشرطة الأدوات"""
        toolbar = QToolBar("الأدوات الرئيسية")
        self.addToolBar(toolbar)
        
        # أدوات الملف
        new_action = QAction("جديد", self)
        new_action.triggered.connect(self.new_document)
        toolbar.addAction(new_action)
        
        open_action = QAction("فتح", self)
        open_action.triggered.connect(self.open_document)
        toolbar.addAction(open_action)
        
        save_action = QAction("حفظ", self)
        save_action.triggered.connect(self.save_document)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # أدوات النوافذ
        cascade_action = QAction("متدرج", self)
        cascade_action.triggered.connect(self.mdi_area.cascadeSubWindows)
        toolbar.addAction(cascade_action)
        
        tile_action = QAction("متجانب", self)
        tile_action.triggered.connect(self.mdi_area.tileSubWindows)
        toolbar.addAction(tile_action)
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # عدد الوثائق
        self.doc_count_label = QLabel("الوثائق: 0")
        self.status_bar.addPermanentWidget(self.doc_count_label)
        
        # الوقت
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        
    def new_document(self):
        """إنشاء وثيقة جديدة"""
        title = f"وثيقة {self.document_counter}"
        child = MDIChildWindow(title)
        
        sub_window = self.mdi_area.addSubWindow(child)
        sub_window.show()
        
        self.document_counter += 1
        self.update_document_count()
        
        self.status_bar.showMessage(f"تم إنشاء وثيقة جديدة: {title}", 2000)
        
    def open_document(self):
        """فتح وثيقة من ملف"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "فتح وثيقة", "", "ملفات النص (*.txt);;جميع الملفات (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                    
                import os
                title = os.path.basename(file_path)
                child = MDIChildWindow(title, content)
                
                sub_window = self.mdi_area.addSubWindow(child)
                sub_window.show()
                
                self.update_document_count()
                self.status_bar.showMessage(f"تم فتح: {title}", 2000)
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"لا يمكن فتح الملف:\n{str(e)}")
                
    def save_document(self):
        """حفظ الوثيقة الحالية"""
        active_window = self.mdi_area.activeSubWindow()
        if not active_window:
            return
            
        child = active_window.widget()
        title = child.windowTitle().rstrip("*")
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ وثيقة", f"{title}.txt", "ملفات النص (*.txt);;جميع الملفات (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(child.get_content())
                    
                import os
                new_title = os.path.basename(file_path)
                child.setWindowTitle(new_title)
                child.document_modified = False
                
                self.status_bar.showMessage(f"تم الحفظ: {new_title}", 2000)
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"لا يمكن حفظ الملف:\n{str(e)}")
                
    def close_document(self):
        """إغلاق الوثيقة الحالية"""
        active_window = self.mdi_area.activeSubWindow()
        if active_window:
            active_window.close()
            self.update_document_count()
            
    def close_all_documents(self):
        """إغلاق جميع الوثائق"""
        self.mdi_area.closeAllSubWindows()
        self.update_document_count()
        
    def toggle_view_mode(self, checked):
        """تبديل وضع العرض"""
        if checked:
            self.mdi_area.setViewMode(QMdiArea.TabbedView)
        else:
            self.mdi_area.setViewMode(QMdiArea.SubWindowView)
            
    def update_document_count(self):
        """تحديث عدد الوثائق"""
        count = len(self.mdi_area.subWindowList())
        self.doc_count_label.setText(f"الوثائق: {count}")
        
    def update_status(self):
        """تحديث شريط الحالة"""
        current_time = QDateTime.currentDateTime().toString("hh:mm:ss")
        self.time_label.setText(f"الوقت: {current_time}")
        
    def show_about(self):
        """عرض معلومات حول MDI"""
        QMessageBox.about(self, "حول MDI", """
        واجهة متعددة الوثائق (MDI)
        
        تسمح بفتح عدة وثائق في نافذة واحدة:
        
        ✓ كل وثيقة في نافذة فرعية منفصلة
        ✓ يمكن ترتيب النوافذ بطرق مختلفة
        ✓ عرض بتبويبات أو نوافذ منفصلة
        ✓ إدارة متقدمة للوثائق المتعددة
        ✓ مشاركة القوائم وأشرطة الأدوات
        
        مفيدة للتطبيقات التي تتعامل مع
        ملفات أو وثائق متعددة في نفس الوقت.
        """)
        
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        self.timer.stop()
        self.mdi_area.closeAllSubWindows()
        event.accept()
