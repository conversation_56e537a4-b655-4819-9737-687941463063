#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النوافذ الأساسية في PySide6
Basic Windows in PySide6
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QTextEdit, QLineEdit, QCheckBox, QRadioButton,
    QSlider, QProgressBar, QSpinBox
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QPalette


class BasicWindow(QWidget):
    """نافذة أساسية بسيطة - QWidget"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نافذة أساسية - QWidget")
        self.setGeometry(200, 200, 500, 400)
        
        # تعيين لون الخلفية
        self.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
            }
        """)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # العنوان
        title = QLabel("نافذة أساسية (QWidget)")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #333;
                padding: 10px;
                background-color: #e0e0e0;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # الوصف
        description = QLabel("""
        هذه نافذة أساسية من نوع QWidget. خصائصها:
        • لا تحتوي على شريط قوائم أو شريط أدوات
        • يمكن تغيير حجمها وتحريكها
        • تحتوي على أزرار التحكم الأساسية (إغلاق، تصغير، تكبير)
        • مناسبة للتطبيقات البسيطة والنوافذ الفرعية
        """)
        description.setWordWrap(True)
        description.setStyleSheet("""
            QLabel {
                background-color: white;
                padding: 15px;
                border-radius: 5px;
                border: 1px solid #ddd;
                line-height: 1.5;
            }
        """)
        layout.addWidget(description)
        
        # منطقة التفاعل
        interaction_group = self.create_interaction_area()
        layout.addWidget(interaction_group)
        
        # منطقة المعلومات
        info_area = QTextEdit()
        info_area.setPlainText("منطقة عرض المعلومات والنتائج...")
        info_area.setMaximumHeight(100)
        info_area.setStyleSheet("""
            QTextEdit {
                background-color: #f9f9f9;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                font-family: 'Courier New', monospace;
            }
        """)
        layout.addWidget(info_area)
        self.info_area = info_area
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        close_btn = QPushButton("إغلاق النافذة")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet(self.get_button_style("#ff4444", "#cc3333"))
        
        minimize_btn = QPushButton("تصغير")
        minimize_btn.clicked.connect(self.showMinimized)
        minimize_btn.setStyleSheet(self.get_button_style("#2196F3", "#1976D2"))
        
        maximize_btn = QPushButton("تكبير/استعادة")
        maximize_btn.clicked.connect(self.toggle_maximize)
        maximize_btn.setStyleSheet(self.get_button_style("#4CAF50", "#45a049"))
        
        buttons_layout.addWidget(minimize_btn)
        buttons_layout.addWidget(maximize_btn)
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)
        
    def create_interaction_area(self):
        """إنشاء منطقة التفاعل"""
        group = QWidget()
        group.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 5px;
                border: 1px solid #ddd;
            }
        """)
        
        layout = QVBoxLayout(group)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # عنوان المجموعة
        group_title = QLabel("منطقة التفاعل")
        group_title.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #333;
                border: none;
                background: none;
            }
        """)
        layout.addWidget(group_title)
        
        # حقل إدخال النص
        text_layout = QHBoxLayout()
        text_layout.addWidget(QLabel("النص:"))
        text_input = QLineEdit()
        text_input.setPlaceholderText("اكتب شيئاً هنا...")
        text_input.textChanged.connect(lambda text: self.update_info(f"تم تغيير النص إلى: {text}"))
        text_layout.addWidget(text_input)
        layout.addLayout(text_layout)
        
        # مربعات الاختيار
        checkbox_layout = QHBoxLayout()
        checkbox1 = QCheckBox("خيار 1")
        checkbox2 = QCheckBox("خيار 2")
        checkbox1.stateChanged.connect(lambda state: self.update_info(f"خيار 1: {'مفعل' if state == 2 else 'معطل'}"))
        checkbox2.stateChanged.connect(lambda state: self.update_info(f"خيار 2: {'مفعل' if state == 2 else 'معطل'}"))
        checkbox_layout.addWidget(checkbox1)
        checkbox_layout.addWidget(checkbox2)
        layout.addLayout(checkbox_layout)
        
        # أزرار الراديو
        radio_layout = QHBoxLayout()
        radio1 = QRadioButton("اختيار أ")
        radio2 = QRadioButton("اختيار ب")
        radio1.toggled.connect(lambda checked: self.update_info("تم اختيار: اختيار أ") if checked else None)
        radio2.toggled.connect(lambda checked: self.update_info("تم اختيار: اختيار ب") if checked else None)
        radio_layout.addWidget(radio1)
        radio_layout.addWidget(radio2)
        layout.addLayout(radio_layout)
        
        # شريط التمرير
        slider_layout = QHBoxLayout()
        slider_layout.addWidget(QLabel("القيمة:"))
        slider = QSlider(Qt.Horizontal)
        slider.setRange(0, 100)
        slider.setValue(50)
        value_label = QLabel("50")
        slider.valueChanged.connect(lambda value: [
            value_label.setText(str(value)),
            self.update_info(f"قيمة الشريط: {value}")
        ])
        slider_layout.addWidget(slider)
        slider_layout.addWidget(value_label)
        layout.addLayout(slider_layout)
        
        # شريط التقدم
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(QLabel("التقدم:"))
        progress = QProgressBar()
        progress.setRange(0, 100)
        progress.setValue(30)
        
        progress_btn = QPushButton("تحديث التقدم")
        progress_btn.clicked.connect(lambda: self.animate_progress(progress))
        progress_btn.setStyleSheet(self.get_button_style("#9C27B0", "#7B1FA2"))
        
        progress_layout.addWidget(progress)
        progress_layout.addWidget(progress_btn)
        layout.addLayout(progress_layout)
        
        return group
    
    def get_button_style(self, bg_color, hover_color):
        """الحصول على نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {hover_color};
                transform: translateY(1px);
            }}
        """
    
    def update_info(self, message):
        """تحديث منطقة المعلومات"""
        current_text = self.info_area.toPlainText()
        new_text = f"{current_text}\n{message}"
        # الاحتفاظ بآخر 10 أسطر فقط
        lines = new_text.split('\n')
        if len(lines) > 10:
            lines = lines[-10:]
        self.info_area.setPlainText('\n'.join(lines))
        
        # التمرير إلى الأسفل
        cursor = self.info_area.textCursor()
        cursor.movePosition(cursor.End)
        self.info_area.setTextCursor(cursor)
    
    def animate_progress(self, progress_bar):
        """تحريك شريط التقدم"""
        self.timer = QTimer()
        self.progress_value = 0
        
        def update_progress():
            self.progress_value += 5
            progress_bar.setValue(self.progress_value)
            self.update_info(f"التقدم: {self.progress_value}%")
            
            if self.progress_value >= 100:
                self.timer.stop()
                self.update_info("اكتمل التقدم!")
        
        self.timer.timeout.connect(update_progress)
        self.timer.start(100)  # كل 100 مللي ثانية
    
    def toggle_maximize(self):
        """تبديل حالة التكبير"""
        if self.isMaximized():
            self.showNormal()
            self.update_info("تم استعادة حجم النافذة")
        else:
            self.showMaximized()
            self.update_info("تم تكبير النافذة")
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        self.update_info("جاري إغلاق النافذة...")
        event.accept()
