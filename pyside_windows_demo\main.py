#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي شامل لأنواع النوافذ في PySide6
Comprehensive PySide6 Windows Types Demo

هذا المشروع يعرض جميع أنواع النوافذ المختلفة في PySide6
This project demonstrates all different window types in PySide6
"""

import sys
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QTextEdit, QGroupBox, QScrollArea
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QIcon

# استيراد أنواع النوافذ المختلفة
from basic_window import BasicWindow
from main_window import CustomMainWindow
from dialog_windows import (
    ModalDialog, ModelessDialog, MessageBoxDemo,
    InputDialogDemo, FileDialogDemo, ColorDialogDemo
)
from popup_windows import PopupWindow, ToolTipWindow
from frameless_window import FramelessWindow
from splash_screen import SplashScreenDemo
from dock_window import DockWindowDemo
from mdi_window import MDIWindowDemo


class WindowsDemoLauncher(QMainWindow):
    """النافذة الرئيسية لتشغيل العروض التوضيحية المختلفة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("عرض توضيحي لأنواع النوافذ في PySide6")
        self.setGeometry(100, 100, 800, 600)
        
        # قائمة لحفظ النوافذ المفتوحة
        self.open_windows = []
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # العنوان
        title_label = QLabel("عرض توضيحي لأنواع النوافذ في PySide6")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # منطقة التمرير للأزرار
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # مجموعة النوافذ الأساسية
        basic_group = self.create_group("النوافذ الأساسية", [
            ("نافذة أساسية", self.show_basic_window),
            ("نافذة رئيسية", self.show_main_window),
            ("نافذة بدون إطار", self.show_frameless_window),
        ])
        scroll_layout.addWidget(basic_group)
        
        # مجموعة نوافذ الحوار
        dialog_group = self.create_group("نوافذ الحوار", [
            ("حوار مودال", self.show_modal_dialog),
            ("حوار غير مودال", self.show_modeless_dialog),
            ("صناديق الرسائل", self.show_message_boxes),
            ("حوار الإدخال", self.show_input_dialog),
            ("حوار الملفات", self.show_file_dialog),
            ("حوار الألوان", self.show_color_dialog),
        ])
        scroll_layout.addWidget(dialog_group)
        
        # مجموعة النوافذ المنبثقة
        popup_group = self.create_group("النوافذ المنبثقة", [
            ("نافذة منبثقة", self.show_popup_window),
            ("نافذة تلميح", self.show_tooltip_window),
        ])
        scroll_layout.addWidget(popup_group)
        
        # مجموعة النوافذ المتخصصة
        special_group = self.create_group("النوافذ المتخصصة", [
            ("شاشة البداية", self.show_splash_screen),
            ("نافذة مع أرصفة", self.show_dock_window),
            ("نافذة متعددة الوثائق", self.show_mdi_window),
        ])
        scroll_layout.addWidget(special_group)
        
        # زر إغلاق جميع النوافذ
        close_all_btn = QPushButton("إغلاق جميع النوافذ")
        close_all_btn.clicked.connect(self.close_all_windows)
        close_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4444;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #cc3333;
            }
        """)
        scroll_layout.addWidget(close_all_btn)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
    def create_group(self, title, buttons):
        """إنشاء مجموعة من الأزرار"""
        group = QGroupBox(title)
        layout = QVBoxLayout(group)
        
        for text, callback in buttons:
            btn = QPushButton(text)
            btn.clicked.connect(callback)
            btn.setMinimumHeight(40)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    padding: 10px;
                    border-radius: 5px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
                QPushButton:pressed {
                    background-color: #3d8b40;
                }
            """)
            layout.addWidget(btn)
            
        return group
    
    # دوال عرض النوافذ المختلفة
    def show_basic_window(self):
        window = BasicWindow()
        self.open_windows.append(window)
        window.show()
    
    def show_main_window(self):
        window = CustomMainWindow()
        self.open_windows.append(window)
        window.show()
    
    def show_frameless_window(self):
        window = FramelessWindow()
        self.open_windows.append(window)
        window.show()
    
    def show_modal_dialog(self):
        dialog = ModalDialog(self)
        dialog.exec()
    
    def show_modeless_dialog(self):
        dialog = ModelessDialog(self)
        self.open_windows.append(dialog)
        dialog.show()
    
    def show_message_boxes(self):
        demo = MessageBoxDemo(self)
        demo.show_all_message_boxes()
    
    def show_input_dialog(self):
        demo = InputDialogDemo(self)
        demo.show_input_dialogs()
    
    def show_file_dialog(self):
        demo = FileDialogDemo(self)
        demo.show_file_dialogs()
    
    def show_color_dialog(self):
        demo = ColorDialogDemo(self)
        demo.show_color_dialog()
    
    def show_popup_window(self):
        window = PopupWindow(self)
        self.open_windows.append(window)
        window.show()
    
    def show_tooltip_window(self):
        window = ToolTipWindow(self)
        self.open_windows.append(window)
        window.show()
    
    def show_splash_screen(self):
        demo = SplashScreenDemo()
        demo.show_splash()
    
    def show_dock_window(self):
        window = DockWindowDemo()
        self.open_windows.append(window)
        window.show()
    
    def show_mdi_window(self):
        window = MDIWindowDemo()
        self.open_windows.append(window)
        window.show()
    
    def close_all_windows(self):
        """إغلاق جميع النوافذ المفتوحة"""
        for window in self.open_windows:
            if window and not window.isHidden():
                window.close()
        self.open_windows.clear()
    
    def closeEvent(self, event):
        """عند إغلاق النافذة الرئيسية"""
        self.close_all_windows()
        event.accept()


def main():
    app = QApplication(sys.argv)
    
    # تعيين خصائص التطبيق
    app.setApplicationName("PySide6 Windows Demo")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Demo Organization")
    
    # إنشاء وعرض النافذة الرئيسية
    launcher = WindowsDemoLauncher()
    launcher.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
